"use client";
import React from "react";
import type { UpdateBlogInput } from "@/app/api/manage/spaces/[spaceId]/articles/[articleId]/route";
import type { CreateBlogInput } from "@/app/api/manage/spaces/[spaceId]/articles/route";
import { useMutation, useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { useParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ArrowLeft, Wand2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import UploadImage from "../../../../../components/upload-image";
import { ContentField } from "../../../../../components/content-field";
import slugify from "slugify";
import { SeoSuggestButton } from "../../../../../components/seo-suggestion-button";
import type { CategoryGetResponse } from "@/app/api/read/category/query";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  seoDescription: z.string().optional(),
  imageUrl: z.string().optional(),
  slug: z.string().optional(),
  articleDate: z.string().optional(),
  language: z.enum(["en", "jp", "zh"]),
  isPublished: z.boolean(),
  isPremium: z.boolean(),
  isReady: z.boolean(),
  categories: z.array(z.string()),
  tags: z.array(z.string()),
});

export type FormValues = z.infer<typeof formSchema>;

// Type for the article response from the new API
interface ArticleResponse {
  id: string;
  slug: string;
  articleDate: string;
  isPremium: boolean;
  isPublished: boolean;
  tags: string[];
  contents: Array<{
    id: string;
    title: string;
    language: string;
    imageUrl: string;
    seoDescription: string;
    content: string;
    isReady: boolean;
  }>;
  categories: Array<{
    id: string;
    labels: Array<{
      id: string;
      name: string;
      language: string;
    }>;
  }>;
}

interface EditContentPageProps {
  contentId?: string;
}

export function EditContentPage({
  contentId: propContentId,
}: EditContentPageProps = {}) {
  const { toast } = useToast();
  const params = useParams();
  const spaceId = params.spaceId as string;
  const contentId = propContentId || (params.articleId as string) || "";

  console.log(params);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      content: "",
      seoDescription: "",
      imageUrl: "",
      slug: "",
      articleDate: "",
      language: "en",
      isPublished: false,
      isPremium: false,
      isReady: false,
      categories: [],
      tags: [],
    },
  });

  const { isPending, data } = useQuery({
    queryFn: async () => {
      if (!contentId) return null;
      const res = await fetch(
        `/api/manage/spaces/${spaceId}/articles/${contentId}`,
      );
      if (!res.ok) {
        throw new Error("Failed to fetch article");
      }
      return res.json() as Promise<ArticleResponse>;
    },
    queryKey: ["article", spaceId, contentId],
    enabled: !!contentId && !!spaceId,
  });

  const { data: categories } = useQuery({
    queryFn: async () => {
      const res = await fetch("/api/read/category");
      return res.json() as Promise<CategoryGetResponse>;
    },
    queryKey: ["categories"],
  });

  const mutation = useMutation({
    mutationFn: async (values: FormValues) => {
      if (!contentId) {
        // Creating new article
        const createData: CreateBlogInput = {
          blog: {
            articleDate: values.articleDate
              ? new Date(values.articleDate)
              : new Date(),
            isPremium: values.isPremium,
            isPublished: values.isPublished,
            slug: values.slug || "",
            tags: values.tags,
            categories: values.categories,
          },
          content: {
            title: values.title,
            imageUrl: values.imageUrl || "",
            seoDescription: values.seoDescription || "",
            content: values.content,
            language: values.language,
          },
        };

        const response = await fetch(`/api/manage/spaces/${spaceId}/articles`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(createData),
        });

        if (!response.ok) {
          throw new Error("Failed to create article");
        }

        return response.json();
      } else {
        // Updating existing article
        const updateData: UpdateBlogInput = {
          blog: {
            articleDate: values.articleDate
              ? new Date(values.articleDate)
              : new Date(),
            isPremium: values.isPremium,
            isPublished: values.isPublished,
            slug: values.slug || "",
            tags: values.tags,
            categories: values.categories,
          },
          content: {
            title: values.title,
            imageUrl: values.imageUrl || "",
            seoDescription: values.seoDescription || "",
            content: values.content,
            language: values.language,
          },
        };

        const response = await fetch(
          `/api/manage/spaces/${spaceId}/articles/${contentId}`,
          {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(updateData),
          },
        );

        if (!response.ok) {
          throw new Error("Failed to update article");
        }

        return response.json();
      }
    },
    onSuccess: (result) => {
      toast({
        title: "Success",
        description: isNewArticle
          ? "Article created successfully!"
          : "Article updated successfully!",
      });

      // If creating a new article, redirect to the edit page
      if (isNewArticle && result?.blog?.id) {
        window.location.href = `/spaces/${spaceId}/articles/${result.blog.id}/edit`;
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const generateSlugFromTitle = () => {
    const title = form.getValues("title");
    const slug = slugify(title, {
      lower: true,
      trim: true,
      locale: "en",
      remove: /[^\w\s-]/g,
    });
    form.setValue("slug", slug);
  };

  // Set form values when data loads
  React.useEffect(() => {
    if (data) {
      // Find the content for the current language or default to first available
      const currentContent =
        data.contents.find((c) => c.language === form.getValues("language")) ||
        data.contents[0];

      const formData: FormValues = {
        title: currentContent?.title || "",
        content: currentContent?.content || "",
        seoDescription: currentContent?.seoDescription || "",
        imageUrl: currentContent?.imageUrl || "",
        slug: data.slug || "",
        articleDate: data.articleDate
          ? new Date(data.articleDate).toISOString().split("T")[0]
          : "",
        language: (currentContent?.language as "en" | "jp" | "zh") || "en",
        isPublished: data.isPublished || false,
        isPremium: data.isPremium || false,
        isReady: currentContent?.isReady || false,
        categories: data.categories?.map((c) => c.id) || [],
        tags: data.tags || [],
      };
      form.reset(formData);
    }
  }, [data, form]);

  // For new articles, contentId will be empty
  const isNewArticle = !contentId;

  if (isPending && !isNewArticle) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={spaceId ? `/spaces/${spaceId}/articles` : "/spaces"}>
          <Button variant="ghost" size="icon" aria-label="Back to articles">
            <ArrowLeft />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">
          {isNewArticle ? "Create Article" : "Edit Article"}
        </h1>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((values) => mutation.mutate(values))}
          className="space-y-6"
        >
          {/* Status checkboxes */}
          <div className="flex gap-6">
            <FormField
              control={form.control}
              name="isPublished"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Published</FormLabel>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isPremium"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Premium</FormLabel>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isReady"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Ready</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>

          {/* Main content area */}
          <div className="flex gap-8 flex-wrap">
            {/* Image upload */}
            <FormField
              control={form.control}
              name="imageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Banner image</FormLabel>
                  <FormControl>
                    <UploadImage
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form fields */}
            <div className="grow space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-4">
                <FormField
                  control={form.control}
                  name="articleDate"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Article date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="language"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Language</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-row space-x-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="en" id="en" />
                            <Label htmlFor="en">English</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="jp" id="jp" />
                            <Label htmlFor="jp">日本語</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="zh" id="zh" />
                            <Label htmlFor="zh">中文</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex gap-4">
                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Slug</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={generateSlugFromTitle}
                        >
                          <Wand2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="categories"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Categories</FormLabel>
                      <Select
                        value={field.value?.[0] || ""}
                        onValueChange={(value) => field.onChange([value])}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((category) => {
                            const label =
                              category.labels.find((l) => l.language === "en")
                                ?.label ||
                              category.labels[0]?.label ||
                              category.id;
                            return (
                              <SelectItem key={category.id} value={category.id}>
                                {label}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="seoDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={3} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Content editor */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl>
                  <ContentField
                    value={field.value}
                    onChange={field.onChange}
                    blogId={data?.id}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Bottom actions */}
          <div className="flex justify-between sticky bottom-0 bg-background border-t py-4">
            <div className="flex gap-4">
              <SeoSuggestButton
                form={form}
                title={form.watch("title")}
                content={form.watch("content")}
                language={form.watch("language")}
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
