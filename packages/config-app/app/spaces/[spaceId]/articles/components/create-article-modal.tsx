"use client";
import type { CreateBlogInput } from "@/app/api/manage/blog/create/route";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  language: z.enum(["en", "jp", "zh"]),
});

type FormValues = z.infer<typeof formSchema>;

const createArticle = async (values: FormValues) => {
  const blogData: CreateBlogInput = {
    blog: {
      articleDate: new Date(),
      isPremium: false,
      isPublished: false,
      slug: "",
    },
    content: {
      title: values.title,
      imageUrl: "",
      seoDescription: "",
      content: "",
      language: values.language,
    },
  };

  await fetch("/api/manage/blog/create", {
    method: "POST",
    body: JSON.stringify(blogData),
  });
};

const LanguageOption = ({
  value,
  label,
  id,
  isSelected,
}: {
  value: string;
  label: string;
  id: string;
  isSelected: boolean;
}) => (
  <div className="flex items-center space-x-2">
    <RadioGroupItem value={value} id={id} className="sr-only" />
    <Label
      htmlFor={id}
      className={`flex-1 cursor-pointer rounded-md border-2 p-1 text-center text-sm font-medium transition-colors ${
        isSelected
          ? "border-primary bg-primary/10"
          : "border-muted hover:border-accent"
      }`}
    >
      {label}
    </Label>
  </div>
);

export function CrateArticleModal() {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      language: "en",
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      await createArticle(values);
      setOpen(false);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["articleList"],
        exact: true,
      });
    } catch (error) {
      console.error("Failed to create article:", error);
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="lg">
          <Plus className="h-4 w-4 mr-2" />
          New Article
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create a new article</DialogTitle>
          <DialogDescription>
            Choose a language and enter a title for your new article.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="language"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-3 gap-4 pt-2"
                      >
                        <LanguageOption
                          value="en"
                          label="English"
                          id="en"
                          isSelected={field.value === "en"}
                        />
                        <LanguageOption
                          value="jp"
                          label="日本語"
                          id="jp"
                          isSelected={field.value === "jp"}
                        />
                        <LanguageOption
                          value="zh"
                          label="中文"
                          id="zh"
                          isSelected={field.value === "zh"}
                        />
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter article title"
                        {...field}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit">Create Article</Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
