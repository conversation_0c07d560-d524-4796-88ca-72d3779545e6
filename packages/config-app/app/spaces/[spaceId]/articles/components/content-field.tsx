import { ForwardRefEditor } from "ui/src/components/forward-ref-editor";
import { useRef } from "react";

export const ContentField = ({
  value,
  onChange,
  blogId,
}: {
  value?: string;
  onChange?: (v: string) => void;
  blogId?: string;
}) => {
  const previousValue = useRef(value ?? "");

  return (
    <ForwardRefEditor
      previousValue={previousValue.current}
      blogId={blogId}
      markdown={value ?? ""}
      onChange={onChange}
      contentEditableClassName="prose dark:prose-invert max-w-none min-h-[500px]"
    />
  );
};
