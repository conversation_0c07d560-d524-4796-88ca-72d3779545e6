import { BlogListResponse } from "@/app/api/manage/blog/list/route";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RenderTitle } from "../components/render-title";
import { TranslateButton } from "../components/translate-button";

const fetchArticleList = async (): Promise<BlogListResponse> => {
  const data = await fetch("/api/manage/blog/list", { method: "GET" });
  return data.json();
};

const columnHelper = createColumnHelper<BlogListResponse[0]>();

export const ArticleList = () => {
  const queryClient = useQueryClient();
  const { isPending, data } = useQuery({
    queryKey: ["articleList"],
    queryFn: fetchArticleList,
  });

  const revalidateList = () =>
    queryClient.invalidateQueries({
      queryKey: ["articleList"],
      exact: true,
    });

  const [operationId, setOperationId] = useState<string | null>(null);

  const columns = [
    columnHelper.accessor("contents", {
      header: "Title",
      cell: ({ row }) => <RenderTitle record={row.original} />,
    }),
    columnHelper.accessor("isPremium", {
      header: "Premium",
      cell: ({ getValue }) => (getValue() ? "Premium" : ""),
      size: 100,
    }),
    columnHelper.accessor("isPublished", {
      header: "Published",
      cell: ({ getValue }) => (
        <span
          className={`inline-flex items-center ${getValue() ? "text-green-600" : "text-gray-500"}`}
        >
          {getValue() ? "● Published" : "○ Draft"}
        </span>
      ),
      size: 120,
    }),
    columnHelper.accessor("articleDate", {
      header: "Article Date",
      cell: ({ getValue }) => {
        const date = getValue();
        return new Date(date).toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
      },
      size: 120,
    }),
    columnHelper.accessor("updatedAt", {
      header: "Last Updated",
      cell: ({ getValue }) => {
        const date = getValue();
        return new Date(date).toLocaleDateString("en-US", {
          year: "2-digit",
          month: "2-digit",
          day: "2-digit",
          hour: "numeric",
          minute: "numeric",
          hour12: false,
        });
      },
      size: 150,
    }),
    columnHelper.accessor("id", {
      header: "Actions",
      cell: ({ getValue, row }) => (
        <div className="flex items-center justify-end space-x-2">
          <TranslateButton record={row.original} onSuccess={revalidateList} />
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setOperationId(getValue());
            }}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      ),
      size: 80,
    }),
  ];

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleDelete = async () => {
    if (!operationId) return;

    try {
      await fetch(`/api/manage/blog/delete`, {
        method: "POST",
        body: JSON.stringify({ id: operationId }),
      });
      setOperationId(null);
      revalidateList();
    } catch (error) {
      console.error("Failed to delete article:", error);
    }
  };

  if (isPending) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <>
      <Dialog open={!!operationId} onOpenChange={() => setOperationId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Article</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this article including all
              translations? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOperationId(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  );
};
